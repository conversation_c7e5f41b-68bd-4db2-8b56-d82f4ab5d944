var searchvisible=0;$("#search-menu").click((function(s){s.preventDefault();var e=$("#search-icon");e.hasClass("ion-ios-search-strong")?(e.addClass("ion-ios-close-empty"),e.removeClass("ion-ios-search-strong")):(e.removeClass("ion-ios-close-empty"),e.addClass("ion-ios-search-strong")),0===searchvisible?($("#search-form").slideDown(200),$("#s").focus(),searchvisible=1):($("#search-form").slideUp(200),searchvisible=0)})),function(s){"use strict";function e(s){return new RegExp("(^|\\s+)"+s+"(\\s+|$)")}var n,i,t;function a(s,e){(n(s,e)?t:i)(s,e)}"classList"in document.documentElement?(n=function(s,e){return s.classList.contains(e)},i=function(s,e){s.classList.add(e)},t=function(s,e){s.classList.remove(e)}):(n=function(s,n){return e(n).test(s.className)},i=function(s,e){n(s,e)||(s.className=s.className+" "+e)},t=function(s,n){s.className=s.className.replace(e(n)," ")});var o={hasClass:n,addClass:i,removeClass:t,toggleClass:a,has:n,add:i,remove:t,toggle:a};"function"==typeof define&&define.amd?define(o):s.classie=o}(window),function(){var s=document.getElementById("trigger-overlay"),e=document.querySelector("div.overlay"),n=e.querySelector("button.overlay-close");function i(){if(classie.has(e,"open")){classie.remove(e,"open"),classie.add(e,"close");var s=function(n){if(support.transitions){if("visibility"!==n.propertyName)return;this.removeEventListener(transEndEventName,s)}classie.remove(e,"close")};support.transitions?e.addEventListener(transEndEventName,s):s()}else classie.has(e,"close")||classie.add(e,"open")}transEndEventNames={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd",msTransition:"MSTransitionEnd",transition:"transitionend"},transEndEventName=transEndEventNames[Modernizr.prefixed("transition")],support={transitions:Modernizr.csstransitions},s.addEventListener("click",i),n.addEventListener("click",i)}();