$(document).ready((function(){function e(){$(".skill-progress").each((function(){var e=$(this),t=e.offset().top,n=t+e.outerHeight(),o=$(window).scrollTop(),i=o+$(window).height();n>=o&&t<=i&&e.addClass("animate")}))}function t(){$(".fade-in, .slide-in-left, .slide-in-right").each((function(){var e=$(this),t=e.offset().top,n=t+e.outerHeight(),o=$(window).scrollTop(),i=o+$(window).height();n>=o&&t<=i&&e.addClass("visible")}))}({init:function(){this.createToggleButton(),this.loadSavedTheme(),this.bindEvents()},createToggleButton:function(){$(".overlay nav ul").append('<li><a href="#" id="mobile-theme-toggle">Toggle Theme</a></li>')},loadSavedTheme:function(){const e=localStorage.getItem("portfolio-theme")||"light";this.setTheme(e)},setTheme:function(e){document.documentElement.setAttribute("data-theme",e),localStorage.setItem("portfolio-theme",e);const t=document.getElementById("theme-toggle");t&&t.setAttribute("aria-label","dark"===e?"Switch to light mode":"Switch to dark mode"),$(document).trigger("themeChanged",[e])},toggleTheme:function(){const e="light"===(document.documentElement.getAttribute("data-theme")||"light")?"dark":"light";this.setTheme(e)},bindEvents:function(){const e=this;$(document).on("click","#theme-toggle",(function(t){t.preventDefault(),e.toggleTheme()})),$(document).on("click","#mobile-theme-toggle",(function(t){t.preventDefault(),e.toggleTheme()})),$(document).on("keydown","#theme-toggle",(function(t){"Enter"!==t.key&&" "!==t.key||(t.preventDefault(),e.toggleTheme())}))}}).init(),$('a[href^="#"]').on("click",(function(e){var t=$(this.getAttribute("href"));t.length&&(e.preventDefault(),$("html, body").stop().animate({scrollTop:t.offset().top-80},1e3))})),$(".project-card").hover((function(){$(this).find(".tech-badges .badge").addClass("hover-effect")}),(function(){$(this).find(".tech-badges .badge").removeClass("hover-effect")})),$(".skills-scroller").hover((function(){$(this).find(".skills-scroll-content").css("animation-play-state","paused")}),(function(){$(this).find(".skills-scroll-content").css("animation-play-state","running")})),e(),t(),$(window).scroll((function(){e(),t()})),$(".btn-cta, .btn-project").on("click",(function(){var e=$(this),t=e.text();e.hasClass("loading")||(e.addClass("loading"),e.text("Loading..."),setTimeout((function(){e.removeClass("loading"),e.text(t)}),2e3))}));var n=document.querySelector(".hero-title");if(n){var o=n.textContent;setTimeout((function(){var e,t,i,a;t=o,i=100,a=0,(e=n).innerHTML="",function n(){a<t.length&&(e.innerHTML+=t.charAt(a),a++,setTimeout(n,i))}()}),500)}$(window).scroll((function(){var e=$(this).scrollTop();$(".hero-section").css("transform","translateY("+.5*e+"px)")})),$("#contact-form").on("submit",(function(e){e.preventDefault();var t=$(this),n=t.find('button[type="submit"]'),o=n.text();n.text("Sending...").prop("disabled",!0),setTimeout((function(){n.text("Message Sent!").removeClass("btn-primary").addClass("btn-success"),setTimeout((function(){n.text(o).prop("disabled",!1).removeClass("btn-success").addClass("btn-primary"),t[0].reset()}),3e3)}),2e3)})),$('a:not([href^="#"]):not([target="_blank"])').on("click",(function(e){var t=$(this).attr("href");!t||"#"===t||t.startsWith("mailto:")||t.startsWith("tel:")||(e.preventDefault(),$("body").append('<div class="loading-overlay"><div class="loading-spinner"></div></div>'),setTimeout((function(){window.location.href=t}),500))})),$(window).on("load",(function(){$(".loading-overlay").fadeOut(500,(function(){$(this).remove()}))})),$("<style>").prop("type","text/css").html('\n            .loading-overlay {\n                position: fixed;\n                top: 0;\n                left: 0;\n                width: 100%;\n                height: 100%;\n                background: rgba(255, 255, 255, 0.9);\n                display: flex;\n                justify-content: center;\n                align-items: center;\n                z-index: 9999;\n                transition: background-color 0.3s ease;\n            }\n\n            [data-theme="dark"] .loading-overlay {\n                background: rgba(17, 24, 39, 0.9);\n            }\n\n            .loading-spinner {\n                width: 40px;\n                height: 40px;\n                border: 4px solid #e2e8f0;\n                border-top: 4px solid #1a365d;\n                border-radius: 50%;\n                animation: spin 1s linear infinite;\n                transition: border-color 0.3s ease;\n            }\n\n            [data-theme="dark"] .loading-spinner {\n                border-color: #374151;\n                border-top-color: #2563eb;\n            }\n\n            @keyframes spin {\n                0% { transform: rotate(0deg); }\n                100% { transform: rotate(360deg); }\n            }\n\n            .skill-progress.animate {\n                transition: width 2s ease-in-out;\n            }\n\n            .badge.hover-effect {\n                transform: scale(1.1);\n                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n            }\n\n            [data-theme="dark"] .badge.hover-effect {\n                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);\n            }\n\n            .btn-cta.loading,\n            .btn-project.loading {\n                opacity: 0.7;\n                cursor: not-allowed;\n            }\n\n            /* Theme toggle container styles */\n            .theme-toggle-container {\n                display: flex;\n                align-items: center;\n            }\n\n            .theme-toggle-container .theme-toggle {\n                margin: 0;\n                padding: 8px;\n            }\n\n            /* Mobile theme toggle styles */\n            #mobile-theme-toggle {\n                color: var(--text-primary) !important;\n                transition: color 0.3s ease;\n            }\n\n            #mobile-theme-toggle:hover {\n                color: var(--rust-accent) !important;\n            }\n        ').appendTo("head"),$(document).on("themeChanged",(function(t,n){$(".skill-progress.animate").length>0&&setTimeout((function(){e()}),300),function(e){$(".dynamic-content").each((function(){"dark"===e?$(this).addClass("dark-theme"):$(this).removeClass("dark-theme")}))}(n)}))}));