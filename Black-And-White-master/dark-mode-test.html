<!DOCTYPE html>
<html lang="en">
<head>
    <title>Dark Mode Test - <PERSON></title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    
    <!-- css -->
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/ionicons.min.css">
    <link rel="stylesheet" href="css/custom.css">
    <link rel="stylesheet" href="css/portfolio.css">
    
    <!-- js -->
    <script src="js/jquery-2.1.3.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
</head>
<body>
    <div class="container" style="padding: 40px 0;">
        <div class="row">
            <div class="col-md-12">
                <h1>Dark Mode Test Page</h1>
                <p>This page tests the dark mode functionality for <PERSON>'s portfolio.</p>
                
                <!-- Theme Toggle Button Test -->
                <div style="margin-bottom: 30px; padding: 20px; border: 1px solid var(--border-light); border-radius: 8px;">
                    <h3>Theme Toggle Visibility Test</h3>
                    <p>The theme toggle button should be clearly visible in both light and dark modes:</p>
                    <div id="header-theme-toggle">
                        <button class="theme-toggle" id="theme-toggle" aria-label="Toggle dark mode" title="Toggle dark/light theme">
                            <svg class="theme-toggle-icon sun-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
                            </svg>
                            <svg class="theme-toggle-icon moon-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                            </svg>
                        </button>
                    </div>
                    <p><strong>Instructions:</strong> Click the sun/moon icon above to toggle between light and dark modes. The button should remain clearly visible in both themes.</p>
                </div>
                
                <!-- Test Components -->
                <div class="row" style="margin-bottom: 30px;">
                    <div class="col-md-6">
                        <div class="project-card">
                            <h3 class="project-title">Test Project Card</h3>
                            <p class="project-description">This is a test project card to verify dark mode styling.</p>
                            <div class="tech-badges">
                                <span class="badge linux">Linux</span>
                                <span class="badge rust">Rust</span>
                                <span class="badge cloud">Cloud</span>
                            </div>
                            <button class="btn-project primary">View Project</button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="skill-category">
                            <h4>Test Skills</h4>
                            <div class="skill-item">
                                <span class="skill-name">C Programming</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-skill="90" style="width: 90%;"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <span class="skill-name">Rust</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-skill="85" style="width: 85%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Test Form -->
                <div class="row">
                    <div class="col-md-8">
                        <h3>Test Contact Form</h3>
                        <form>
                            <div class="form-group">
                                <label for="test-name">Name</label>
                                <input type="text" class="form-control" id="test-name" placeholder="Your name">
                            </div>
                            <div class="form-group">
                                <label for="test-email">Email</label>
                                <input type="email" class="form-control" id="test-email" placeholder="Your email">
                            </div>
                            <div class="form-group">
                                <label for="test-message">Message</label>
                                <textarea class="form-control" id="test-message" rows="4" placeholder="Your message"></textarea>
                            </div>
                            <button type="submit" class="btn-cta">Send Message</button>
                        </form>
                    </div>
                </div>
                
                <!-- Test CTA Section -->
                <div class="cta-section" style="margin-top: 40px; padding: 40px; text-align: center;">
                    <h2 class="cta-title">Test Call to Action</h2>
                    <p class="cta-description">This tests the CTA section in both light and dark modes.</p>
                    <button class="btn-cta">Get Started</button>
                </div>
                
                <!-- Theme Status Display -->
                <div style="margin-top: 30px; padding: 20px; border: 1px solid var(--border-light); border-radius: 8px;">
                    <h4>Current Theme Status</h4>
                    <p>Current theme: <span id="current-theme">Loading...</span></p>
                    <p>Theme persistence: <span id="theme-persistence">Testing...</span></p>
                </div>
            </div>
        </div>
    </div>
    
    <script src="js/portfolio.js"></script>
    <script>
        // Additional test script
        $(document).ready(function() {
            function updateThemeStatus() {
                const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
                const savedTheme = localStorage.getItem('portfolio-theme') || 'none';
                
                $('#current-theme').text(currentTheme);
                $('#theme-persistence').text('Saved: ' + savedTheme);
            }
            
            // Update status on load
            updateThemeStatus();
            
            // Update status on theme change
            $(document).on('themeChanged', function() {
                setTimeout(updateThemeStatus, 100);
            });
        });
    </script>
</body>
</html>
