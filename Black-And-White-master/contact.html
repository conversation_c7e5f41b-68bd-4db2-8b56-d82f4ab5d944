<!DOCTYPE html>
<html lang="en">
	<head>
		<title><PERSON> - <PERSON> | Linux Kernel Engineer <PERSON></title>

		<!-- meta -->
		<meta charset="UTF-8">
	    <meta name="viewport" content="width=device-width, initial-scale=1">
	    <meta name="description" content="Contact Daniel Orji - Aspiring Linux Kernel Engineer. Available for collaboration on C/Rust development, system programming, and cybersecurity projects. Open to relocation opportunities.">
	    <meta name="keywords" content="Daniel Orji <PERSON>, Linux Kernel Engineer, Hire Developer, C Programming, Rust Developer, Collaboration, Relocation">
	    <meta name="author" content="Daniel Orji">

	    <!-- Open Graph / Social Media -->
	    <meta property="og:type" content="website">
	    <meta property="og:title" content="Contact Daniel Orji - Linux Kernel Engineer">
	    <meta property="og:description" content="Get in touch with Daniel Orji for Linux kernel development and system programming opportunities">
	    <meta property="og:url" content="">
	    <meta property="og:image" content="">

	    <!-- css -->
		<link rel="stylesheet" href="css/bootstrap.min.css">
		<link rel="stylesheet" href="css/ionicons.min.css">
		<link rel="stylesheet" href="css/pace.css">
	    <link rel="stylesheet" href="css/custom.css">
	    <link rel="stylesheet" href="css/portfolio.css">

	    <!-- js -->
	    <script src="js/jquery-2.1.3.min.js"></script>
	    <script src="js/bootstrap.min.js"></script>
	    <script src="js/pace.min.js"></script>
	    <script src="js/modernizr.custom.js"></script>
	</head>

	<body id="page">
		<div class="container">	
			<header id="site-header">
				<div class="row">
					<div class="col-md-4 col-sm-5 col-xs-8">
						<div class="logo">
							<h1><a href="index.html"><b>Daniel</b> Orji</a></h1>
						</div>
					</div><!-- col-md-4 -->
					<div class="col-md-8 col-sm-7 col-xs-4">
						<nav class="main-nav" role="navigation">
							<div class="navbar-header">
  								<button type="button" id="trigger-overlay" class="navbar-toggle">
    								<span class="ion-navicon"></span>
  								</button>
							</div>

							<div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
  								<ul class="nav navbar-nav navbar-right">
    								<li class="cl-effect-11"><a href="index.html" data-hover="Home">Home</a></li>
    								<li class="cl-effect-11"><a href="projects.html" data-hover="Projects">Projects</a></li>
    								<li class="cl-effect-11"><a href="about.html" data-hover="About">About</a></li>
    								<li class="cl-effect-11"><a href="contact.html" data-hover="Contact">Contact</a></li>
    								<li class="cl-effect-11"><a href="support.html" data-hover="Support">Support</a></li>
  								</ul>
							</div><!-- /.navbar-collapse -->
						</nav>
						<div id="header-theme-toggle">
							<button class="theme-toggle" id="theme-toggle" aria-label="Toggle dark mode" title="Toggle dark/light theme">
								<svg class="theme-toggle-icon sun-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
									<path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
								</svg>
								<svg class="theme-toggle-icon moon-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
									<path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
								</svg>
							</button>
						</div>
					</div><!-- col-md-8 -->
				</div>
			</header>
		</div>

		<div class="content-body">
			<div class="container">
				<div class="row">
					<main class="col-md-12">
						<h1 class="page-title">Contact Daniel Orji</h1>
						<article class="post">
							<div class="entry-content clearfix">
								<!-- Contact Information -->
								<section class="contact-info-section">
									<h2>Get In Touch</h2>
									<p>I'm actively seeking opportunities in Linux kernel development and system programming. Available for collaboration on C/Rust development, cybersecurity projects, and infrastructure automation.</p>
									<p><strong>Open to relocation opportunities in the United States.</strong></p>

									<div class="contact-details">
										<div class="contact-item">
											<h3>📧 Email Addresses</h3>
											<p><strong>Professional:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
											<p><strong>General:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
										</div>

										<div class="contact-item">
											<h3>💼 Professional Focus</h3>
											<ul>
												<li>Linux Kernel Development</li>
												<li>C/Rust Programming</li>
												<li>System Programming & Security</li>
												<li>Cloud Infrastructure (AWS, Proxmox VE)</li>
												<li>Cybersecurity & IT Support</li>
											</ul>
										</div>

										<div class="contact-item">
											<h3>🎯 Seeking Opportunities</h3>
											<ul>
												<li>Linux Kernel Engineer positions</li>
												<li>System Programming roles</li>
												<li>Cloud Engineer opportunities</li>
												<li>Cybersecurity Analyst positions</li>
												<li>Remote or US-based positions</li>
											</ul>
										</div>
									</div>
								</section>

								<div class="height-40px"></div>

								<!-- Contact Form -->
								<section class="contact-form-section">
									<h2>Send a Message</h2>
									<form action="mailto:<EMAIL>" method="post" enctype="text/plain" class="contact-form">
										<div class="row">
											<div class="col-md-6 col-md-offset-3">
												<input type="text" name="name" placeholder="Your Name" required>
												<input type="email" name="email" placeholder="Your Email" required>
												<input type="text" name="subject" placeholder="Subject" required>
												<textarea name="message" rows="7" placeholder="Your Message" required></textarea>
												<button type="submit" class="btn-send btn-5 btn-5b ion-ios-paperplane"><span>Send Message</span></button>
											</div>
										</div>	<!-- row -->
									</form>
								</section>
							</div>
						</article>
					</main>
				</div>
			</div>
		</div>
		<footer id="site-footer">
			<div class="container">
				<div class="row">
					<div class="col-md-12">
						<p class="copyright">&copy; 2024 Daniel Orji - Linux Kernel Engineer</p>
					</div>
				</div>
			</div>
		</footer>

		<!-- Mobile Menu -->
		<div class="overlay overlay-hugeinc">
			<button type="button" class="overlay-close"><span class="ion-ios-close-empty"></span></button>
			<nav>
				<ul>
					<li><a href="index.html">Home</a></li>
					<li><a href="projects.html">Projects</a></li>
					<li><a href="about.html">About</a></li>
					<li><a href="contact.html">Contact</a></li>
					<li><a href="support.html">Support</a></li>
				</ul>
			</nav>
		</div>

		<script src="js/script.js"></script>
		<script src="js/portfolio.js"></script>
		
	</body>
</html>
