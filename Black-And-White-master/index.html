<!DOCTYPE html>
<html lang="en">
	<head>
		<title><PERSON> - <PERSON>piring Linux Kernel Engineer | Portfolio</title>

		<!-- meta -->
		<meta charset="UTF-8">
	    <meta name="viewport" content="width=device-width, initial-scale=1">
	    <meta name="description" content="<PERSON> - Aspiring Linux Kernel Engineer specializing in C/Rust development, Linux systems, and cybersecurity. Available for relocation to the United States.">
	    <meta name="keywords" content="Linux Kernel Engineer, C Programming, Rust Developer, Cybersecurity, Proxmox, AWS, DevOps">
	    <meta name="author" content="Daniel Orji">

	    <!-- Open Graph / Social Media -->
	    <meta property="og:type" content="website">
	    <meta property="og:title" content="Daniel Orji - Linux Kernel Engineer Portfolio">
	    <meta property="og:description" content="Aspiring Linux Kernel Engineer specializing in C/Rust development and cybersecurity">
	    <meta property="og:url" content="">
	    <meta property="og:image" content="">

	    <!-- css -->
		<link rel="stylesheet" href="css/bootstrap.min.css">
		<link rel="stylesheet" href="css/ionicons.min.css">
		<link rel="stylesheet" href="css/pace.css">
	    <link rel="stylesheet" href="css/custom.css">
	    <link rel="stylesheet" href="css/portfolio.css">

	    <!-- js -->
	    <script src="js/jquery-2.1.3.min.js"></script>
	    <script src="js/bootstrap.min.js"></script>
	    <script src="js/pace.min.js"></script>
	    <script src="js/modernizr.custom.js"></script>
	</head>

	<body>
		<!-- Skip Navigation -->
		<a href="#main-content" class="skip-nav">Skip to main content</a>

		<div class="container">
			<header id="site-header" role="banner">
				<div class="row">
					<div class="col-md-4 col-sm-5 col-xs-8">
						<div class="logo">
							<h1><a href="index.html"><b>Daniel</b> Orji</a></h1>
						</div>
					</div><!-- col-md-4 -->
					<div class="col-md-8 col-sm-7 col-xs-4">
						<nav class="main-nav" role="navigation">
							<div class="navbar-header">
  								<button type="button" id="trigger-overlay" class="navbar-toggle">
    								<span class="ion-navicon"></span>
  								</button>
							</div>

							<div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
  								<ul class="nav navbar-nav navbar-right">
    								<li class="cl-effect-11"><a href="index.html" data-hover="Home">Home</a></li>
    								<li class="cl-effect-11"><a href="projects.html" data-hover="Projects">Projects</a></li>
    								<li class="cl-effect-11"><a href="about.html" data-hover="About">About</a></li>
    								<li class="cl-effect-11"><a href="contact.html" data-hover="Contact">Contact</a></li>
    								<li class="cl-effect-11"><a href="support.html" data-hover="Support">Support</a></li>
  								</ul>
							</div><!-- /.navbar-collapse -->
						</nav>
						<div id="header-theme-toggle">
							<button class="theme-toggle" id="theme-toggle" aria-label="Toggle dark mode" title="Toggle dark/light theme">
								<svg class="theme-toggle-icon sun-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
									<path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
								</svg>
								<svg class="theme-toggle-icon moon-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
									<path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
								</svg>
							</button>
						</div>
					</div><!-- col-md-8 -->
				</div>
			</header>
		</div>

		<!-- Hero Section -->
	<section class="hero-section" role="banner" aria-labelledby="hero-title">
		<div class="container">
			<div class="row">
				<div class="col-md-12">
					<h1 id="hero-title" class="hero-title">Daniel Orji</h1>
					<h2 class="hero-subtitle">Aspiring Linux Kernel Engineer</h2>
					<p class="hero-description">
						Early Career Computer Scientist focused on Linux kernel development, system programming, and cybersecurity.
						Experienced in cloud infrastructure, IT support, and building reliable systems with C, Python, and emerging Rust skills.
					</p>

					<!-- Animated Skills Scroller -->
					<div class="skills-scroller" role="marquee" aria-label="Technical skills showcase">
						<div class="skills-scroll-content">
							C Programming • Python Scripting • Rust Learning • AWS Cloud • Proxmox VE • Docker • Linux Systems • Cybersecurity • Networking • System Administration
						</div>
					</div>

					<div class="hero-actions">
						<a href="projects.html" class="btn-cta" role="button">View My Projects</a>
						<a href="contact.html" class="btn-project" style="margin-left: 20px;" role="button">Get In Touch</a>
					</div>
				</div>
			</div>
		</div>
	</section>

	<div class="content-body">
			<div class="container">
				<div class="row">
					<main id="main-content" class="col-md-12" role="main">
						<!-- Skills Matrix Section -->
						<section class="skills-matrix">
							<h2 class="section-title">Technical Expertise</h2>
							<div class="skills-grid">
								<div class="skill-category">
									<h3>Programming Languages</h3>
									<div class="skill-item">
										<div class="skill-name">
											<span>C (Foundational)</span>
											<span>75%</span>
										</div>
										<div class="skill-bar">
											<div class="skill-progress c-lang"></div>
										</div>
									</div>
									<div class="skill-item">
										<div class="skill-name">
											<span>Rust (Learning)</span>
											<span>45%</span>
										</div>
										<div class="skill-bar">
											<div class="skill-progress rust-lang"></div>
										</div>
									</div>
									<div class="skill-item">
										<div class="skill-name">
											<span>Python (Intermediate-Advanced)</span>
											<span>85%</span>
										</div>
										<div class="skill-bar">
											<div class="skill-progress python"></div>
										</div>
									</div>
									<div class="tech-badges">
										<span class="badge linux">C</span>
										<span class="badge rust">Rust</span>
										<span class="badge cloud">Python</span>
										<span class="badge">Bash</span>
									</div>
								</div>

								<div class="skill-category">
									<h3>Systems & Infrastructure</h3>
									<div class="skill-item">
										<div class="skill-name">
											<span>Linux Systems</span>
											<span>85%</span>
										</div>
										<div class="skill-bar">
											<div class="skill-progress linux"></div>
										</div>
									</div>
									<div class="skill-item">
										<div class="skill-name">
											<span>Cloud & DevOps</span>
											<span>75%</span>
										</div>
										<div class="skill-bar">
											<div class="skill-progress cloud"></div>
										</div>
									</div>
									<div class="tech-badges">
										<span class="badge linux">Ubuntu</span>
										<span class="badge linux">Proxmox VE</span>
										<span class="badge cloud">AWS</span>
										<span class="badge cloud">Docker</span>
									</div>
								</div>

								<div class="skill-category">
									<h3>Specializations</h3>
									<div class="tech-badges">
										<span class="badge linux">Kernel Development</span>
										<span class="badge rust">Device Drivers</span>
										<span class="badge cloud">System Security</span>
										<span class="badge">Network Programming</span>
										<span class="badge linux">Memory Management</span>
										<span class="badge rust">Concurrency</span>
									</div>
									<p style="margin-top: 20px; color: var(--text-secondary); text-align: center;">
										Focus on low-level system programming, security hardening, and performance optimization
									</p>
								</div>
							</div>
						</section>

						<!-- Featured Projects Section -->
						<section class="featured-projects">
							<h2 class="section-title">Featured Projects</h2>
							<div class="projects-grid">
								<div class="project-card">
									<h3 class="project-title">Proxmox VE Infrastructure</h3>
									<p class="project-description">
										Local infrastructure management with DNS servers, VS Code remote development environment,
										and service isolation using Proxmox VE virtualization platform.
									</p>
									<div class="tech-badges">
										<span class="badge linux">Proxmox VE</span>
										<span class="badge linux">Linux</span>
										<span class="badge">Bash</span>
										<span class="badge cloud">Networking</span>
									</div>
									<div class="project-links">
										<a href="projects.html#proxmox" class="btn-project primary">View Details</a>
										<a href="#" class="btn-project">Live Demo</a>
									</div>
								</div>

								<div class="project-card">
									<h3 class="project-title">Linux Kernel Modules</h3>
									<p class="project-description">
										Custom kernel modules and device drivers development focusing on memory management,
										system calls, and hardware interaction using C programming.
									</p>
									<div class="tech-badges">
										<span class="badge linux">C</span>
										<span class="badge linux">Kernel</span>
										<span class="badge rust">Device Drivers</span>
										<span class="badge">Assembly</span>
									</div>
									<div class="project-links">
										<a href="projects.html#kernel" class="btn-project primary">View Details</a>
										<a href="#" class="btn-project">Source Code</a>
									</div>
								</div>

								<div class="project-card">
									<h3 class="project-title">Rust System Tools</h3>
									<p class="project-description">
										High-performance system utilities and network programming tools built with Rust,
										focusing on memory safety and concurrent programming patterns.
									</p>
									<div class="tech-badges">
										<span class="badge rust">Rust</span>
										<span class="badge rust">Concurrency</span>
										<span class="badge cloud">Networking</span>
										<span class="badge">Performance</span>
									</div>
									<div class="project-links">
										<a href="projects.html#rust-tools" class="btn-project primary">View Details</a>
										<a href="#" class="btn-project">Source Code</a>
									</div>
								</div>
							</div>
						</section>

						<!-- Call to Action Section -->
						<section class="cta-section">
							<h2 class="cta-title">Ready to Collaborate?</h2>
							<p class="cta-description">
								I'm actively seeking opportunities in Linux kernel development and system programming.
								Available for relocation to the United States for the right opportunity.
							</p>
							<a href="contact.html" class="btn-cta">Get In Touch</a>
						</section>
					</main>

				</div>
			</div>
		</div>

		<footer id="site-footer">
			<div class="container">
				<div class="row">
					<div class="col-md-12">
						<p class="copyright">&copy; 2024 Daniel Orji - Linux Kernel Engineer</p>
					</div>
				</div>
			</div>
		</footer>

		<!-- Mobile Menu -->
		<div class="overlay overlay-hugeinc" role="dialog" aria-labelledby="mobile-menu-title" aria-hidden="true">
			<button type="button" class="overlay-close" aria-label="Close mobile menu">
				<span class="ion-ios-close-empty" aria-hidden="true"></span>
			</button>
			<nav role="navigation" aria-labelledby="mobile-menu-title">
				<h2 id="mobile-menu-title" class="sr-only">Mobile Navigation Menu</h2>
				<ul>
					<li><a href="index.html">Home</a></li>
					<li><a href="projects.html">Projects</a></li>
					<li><a href="about.html">About</a></li>
					<li><a href="contact.html">Contact</a></li>
					<li><a href="support.html">Support</a></li>
				</ul>
			</nav>
		</div>

		<script src="js/script.js"></script>
		<script src="js/portfolio.js"></script>

	</body>
</html>