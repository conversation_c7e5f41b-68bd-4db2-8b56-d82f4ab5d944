<!DOCTYPE html>
<html lang="en">
	<head>
		<title>About - <PERSON> | Linux Kernel Engineer <PERSON></title>

		<!-- meta -->
		<meta charset="UTF-8">
	    <meta name="viewport" content="width=device-width, initial-scale=1">
	    <meta name="description" content="About <PERSON> - Aspiring Linux Kernel Engineer with expertise in C/Rust development, system programming, and cybersecurity. Professional background and skills.">
	    <meta name="keywords" content="<PERSON>, Linux Kernel Engineer, C Programming, Rust Developer, About, Resume, Skills, Experience">
	    <meta name="author" content="Daniel Orji">

	    <!-- Open Graph / Social Media -->
	    <meta property="og:type" content="website">
	    <meta property="og:title" content="About Daniel Orji - Linux Kernel Engineer">
	    <meta property="og:description" content="Professional background and expertise of <PERSON>, aspiring Linux Kernel Engineer">
	    <meta property="og:url" content="">
	    <meta property="og:image" content="">

	    <!-- css -->
		<link rel="stylesheet" href="css/bootstrap.min.css">
		<link rel="stylesheet" href="css/ionicons.min.css">
		<link rel="stylesheet" href="css/pace.css">
	    <link rel="stylesheet" href="css/custom.css">
	    <link rel="stylesheet" href="css/portfolio.css">

	    <!-- js -->
	    <script src="js/jquery-2.1.3.min.js"></script>
	    <script src="js/bootstrap.min.js"></script>
	    <script src="js/pace.min.js"></script>
	    <script src="js/modernizr.custom.js"></script>
	</head>

	<body id="page">
		<div class="container">	
			<header id="site-header">
				<div class="row">
					<div class="col-md-4 col-sm-5 col-xs-8">
						<div class="logo">
							<h1><a href="index.html"><b>Daniel</b> Orji</a></h1>
						</div>
					</div><!-- col-md-4 -->
					<div class="col-md-8 col-sm-7 col-xs-4">
						<nav class="main-nav" role="navigation">
							<div class="navbar-header">
  								<button type="button" id="trigger-overlay" class="navbar-toggle">
    								<span class="ion-navicon"></span>
  								</button>
							</div>

							<div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
  								<ul class="nav navbar-nav navbar-right">
    								<li class="cl-effect-11"><a href="index.html" data-hover="Home">Home</a></li>
    								<li class="cl-effect-11"><a href="projects.html" data-hover="Projects">Projects</a></li>
    								<li class="cl-effect-11"><a href="about.html" data-hover="About">About</a></li>
    								<li class="cl-effect-11"><a href="contact.html" data-hover="Contact">Contact</a></li>
    								<li class="cl-effect-11"><a href="support.html" data-hover="Support">Support</a></li>
  								</ul>
							</div><!-- /.navbar-collapse -->
						</nav>
						<div id="header-theme-toggle">
							<button class="theme-toggle" id="theme-toggle" aria-label="Toggle dark mode" title="Toggle dark/light theme">
								<svg class="theme-toggle-icon sun-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
									<path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
								</svg>
								<svg class="theme-toggle-icon moon-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
									<path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
								</svg>
							</button>
						</div>
					</div><!-- col-md-8 -->
				</div>
			</header>
		</div>

		<div class="content-body">
			<div class="container">
				<div class="row">
					<main class="col-md-12">
						<h1 class="page-title">About Daniel Orji</h1>
						<article class="post">
							<div class="entry-content clearfix">
								<figure class="img-responsive-center">
									<img class="img-responsive" src="img/me.jpg" alt="Daniel Orji - Aspiring Linux Kernel Engineer">
								</figure>
								
								<!-- Professional Identity -->
								<section class="about-section">
									<h2>Professional Identity & Aspirations</h2>
									<p>I am an <strong>Early Career Computer Scientist and Aspiring Linux Kernel Engineer</strong> with a primary career goal of becoming a Linux Kernel Engineer. I am particularly enthusiastic about developing high-quality C (and aspiring Rust) code for the Linux kernel, aiming to contribute to critical kernel development, security hardening, and driver integration for Ubuntu.</p>
									<p>My professional interests span across Cloud Engineering, IT Support, Cybersecurity Analysis, and System Administration, with a strong foundation in both theoretical knowledge and practical application.</p>
								</section>

								<!-- Early Life & Background -->
								<section class="about-section">
									<h2>Background & Journey into Technology</h2>
									<p>My interest in computer systems peaked in my early teenage years when I played a lot of mobile video games on button phones. This curiosity and problem-solving abilities guided me to study cybersecurity and pursue a career in technology.</p>
									<p>Despite initially enrolling as a Geologist at the Federal University of Technology and having to drop out due to lack of funding, this setback did not deter me. I gained customer service experience in sales and worked as an electrical engineer apprentice, troubleshooting electrical and electronics issues and deploying solar panels.</p>
									<p>I finally got into tech from there, self-initiating my learning journey by pursuing CCNA certification, various certifications, and knowledge in fields like cybersecurity, Linux (including reading "Linux basics for hackers"), cryptocurrency, and Web3. This demonstrates my strong self-motivated and persistent approach to professional development despite challenges.</p>
								</section>

								<!-- Education & Certifications -->
								<section class="about-section">
									<h2>Education & Certifications</h2>
									<div class="education-grid">
										<div class="education-item">
											<h3>Academic Qualifications</h3>
											<ul>
												<li><strong>Bachelor of Applied Science (BASc) in Computer Science</strong> - University of the People (Expected December 2024)</li>
												<li><strong>Associate's Degree, Computer Systems Networking and Telecommunications</strong> - Cisco Networking Academy (February 2023)</li>
												<li><strong>Associate's Degree, Computer Systems Networking and Telecommunications</strong> - Coursera (June 2023)</li>
												<li><strong>Associate's Degree, Computer Systems Networking and Telecommunications</strong> - freeCodeCamp (May 2023)</li>
												<li><strong>SSCE</strong> - Christians Model Secondary School (2017)</li>
											</ul>
										</div>
										<div class="education-item">
											<h3>Professional Certifications</h3>
											<ul>
												<li>Certified in Cybersecurity – ISC2</li>
												<li>Google IT Support Professional Certificate – Coursera</li>
												<li>Google Cybersecurity Professional Certificate – Coursera</li>
												<li>CompTIA A+ – Cybrary (220-1001 & 220-1002)</li>
												<li>Cisco Networking Essentials – Cisco Networking Academy</li>
												<li>AWS Cloud Fundamentals – AWS Training & Certification</li>
												<li>CyberOps Associate – Cisco Networking Academy</li>
												<li>ALX Artificial Intelligence Career Essentials (AICE)</li>
											</ul>
										</div>
									</div>
								</section>

								<!-- Professional Experience -->
								<section class="about-section">
									<h2>Professional Experience</h2>
									<div class="experience-item">
										<h3>Cloud Engineer / IT Support Specialist (Freelance & Contract, 2022 – Present)</h3>
										<ul>
											<li>Maintained and configured cloud infrastructure using AWS services (EC2, S3, IAM, VPC)</li>
											<li>Designed, deployed, and maintained cloud-based infrastructures ensuring high availability</li>
											<li>Implemented system monitoring and health checks for hosted services</li>
											<li>Administered Linux and Windows systems, performing updates, patching, and access management</li>
											<li>Utilized Docker and Proxmox VE to manage virtual machines and development environments</li>
											<li>Provided remote technical support, diagnosing complex connectivity and system issues</li>
											<li>Documented technical incidents and resolutions with clear technical records</li>
										</ul>
									</div>
									<div class="experience-item">
										<h3>Electrical Engineering Support (Oguchi Solar Energy and Repairs, 2019 – 2022)</h3>
										<ul>
											<li>Analyzed and troubleshot electrical systems, developing strong analytical abilities</li>
											<li>Gained practical experience with hardware infrastructure including UPS and solar systems</li>
											<li>Installed, tested, and troubleshot electrical systems for solar energy projects</li>
											<li>Provided customer service and field services support</li>
											<li>Gained experience in structured cabling and network deployment</li>
										</ul>
									</div>
									<div class="experience-item">
										<h3>Supply Chain Supervisor (Chibyke Exclusive Boutique, 2023 – 2025)</h3>
										<ul>
											<li>Demonstrated project management skills by streamlining logistics processes</li>
											<li>Managed inventory and logistics systems with attention to process efficiency</li>
											<li>Cultivated professional communication and collaboration skills</li>
											<li>Ensured security of business assets and reliable delivery of payload</li>
										</ul>
									</div>
								</section>

								<!-- J3scandjove Involvement -->
								<section class="about-section">
									<h2>J3scandjove & Professional Development</h2>
									<p>I am closely associated with <strong>J3scandjove</strong>, a web domain management company that focuses on providing reliable IT and business operations with an emphasis on security, redundancy, and reliability for personal and small business owners.</p>
									<p>A significant initiative of J3scandjove is its private training community, dedicated to fostering professional development in IT and cybersecurity, which aligns with my personal drive for continuous learning and knowledge sharing.</p>
								</section>

								<!-- Professional Attributes -->
								<section class="about-section">
									<h2>Professional Attributes</h2>
									<div class="attributes-grid">
										<div class="attribute-item">
											<h4>Collaborative Teamwork</h4>
											<p>Proven ability to thrive in remote, globally distributed team environments with excellent written and verbal English communication skills.</p>
										</div>
										<div class="attribute-item">
											<h4>Problem-Solving & Troubleshooting</h4>
											<p>Strong analytical skills with demonstrated ability to diagnose and resolve complex technical issues across various systems and platforms.</p>
										</div>
										<div class="attribute-item">
											<h4>Continuous Learning</h4>
											<p>Motivated self-starter committed to continuous learning, proactive in exploring new technologies like Rust, Web development, cryptocurrency, and Web3.</p>
										</div>
										<div class="attribute-item">
											<h4>Adaptability & Initiative</h4>
											<p>Well-organized with strong project management skills, adaptable to flexible shifts and dynamic service environments.</p>
										</div>
									</div>
								</section>

								<div class="height-40px"></div>
								<h2 class="title text-center">Connect With Me</h2>
								<div class="contact-info text-center">
									<p><strong>Email:</strong> <EMAIL> | <EMAIL></p>
									<p><strong>Available for:</strong> Linux Kernel Development, System Programming, Cybersecurity Projects</p>
									<p><strong>Relocation:</strong> Open to opportunities in the United States</p>
								</div>
							</div>
						</article>
					</main>
				</div>
			</div>
		</div>
		<footer id="site-footer">
			<div class="container">
				<div class="row">
					<div class="col-md-12">
						<p class="copyright">&copy; 2024 Daniel Orji - Linux Kernel Engineer</p>
					</div>
				</div>
			</div>
		</footer>

		<!-- Mobile Menu -->
		<div class="overlay overlay-hugeinc">
			<button type="button" class="overlay-close"><span class="ion-ios-close-empty"></span></button>
			<nav>
				<ul>
					<li><a href="index.html">Home</a></li>
					<li><a href="projects.html">Projects</a></li>
					<li><a href="about.html">About</a></li>
					<li><a href="contact.html">Contact</a></li>
					<li><a href="support.html">Support</a></li>
				</ul>
			</nav>
		</div>

		<script src="js/script.js"></script>
		<script src="js/portfolio.js"></script>
		
	</body>
</html>
